name: Release Production

on:
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow_ref }}
  cancel-in-progress: false

jobs:
  setup:
    name: Setup
    runs-on: ubuntu-latest
    environment: production
    outputs:
      app-version: ${{ steps.app-version.outputs.app-version }}
      matrix: ${{ steps.set-matrix.outputs.matrix }}
      private-subnet-ids: ${{ steps.get-outputs.outputs.private-subnet-ids }}
      api-service-security-group-id: ${{ steps.get-outputs.outputs.api-service-security-group-id }}
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - uses: ./.github/actions/prepare

      - name: Install Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.6.2

      - name: Get the application version
        id: app-version
        uses: ./.github/actions/app-version

      - name: Set matrix
        id: set-matrix
        run: |
          echo 'matrix={"app-name":["api-service", "panel", "market-intelligence"]}' >> $GITHUB_OUTPUT

      - uses: ./.github/actions/setup-aws
        with:
          aws-region: ${{ vars.AWS_REGION }}
          aws-account-id: ${{ secrets.AWS_ACCOUNT_ID }}
          environment: production

      - name: Deploy required infrastructure stacks
        run: pnpm nx run infrastructure:deploy-minimum-stack:production
        env:
          AWS_REGION: ${{ vars.AWS_REGION }}
          AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}

      - name: Get outputs
        id: get-outputs
        run: |
          mkdir -p dist
          pnpm nx run infrastructure:output:production --stack=network-stack
          pnpm nx run infrastructure:output:production --stack=auth-stack
          export NETWORK_STACK_OUTPUTS=$(cat dist/network-stack.json)
          export AUTH_STACK_OUTPUTS=$(cat dist/auth-stack.json)
          echo "private-subnet-ids=$(echo $NETWORK_STACK_OUTPUTS | jq -r '.["private-subnet-ids"].value | join(",")')" >> $GITHUB_OUTPUT
          echo "api-service-security-group-id=$(echo $AUTH_STACK_OUTPUTS | jq -r '."api-service-security-group-id".value')" >> $GITHUB_OUTPUT

  build:
    name: Build ${{ matrix.app-name }}
    needs: setup
    runs-on: ubuntu-latest
    environment: production
    permissions:
      id-token: write
      contents: read
    strategy:
      matrix: ${{ fromJson(needs.setup.outputs.matrix) }}
      fail-fast: false
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Build & push ${{ matrix.app-name }}
        uses: ./.github/actions/build-push-app
        with:
          project-name: "swift"
          app-name: ${{ matrix.app-name }}
          image-tag-prefix: "main"
          environment: "production"
          aws-account-id: ${{ secrets.AWS_ACCOUNT_ID }}
          aws-region: ${{ vars.AWS_REGION }}
          dockerhub-username: ${{ secrets.DOCKERHUB_USERNAME }}
          dockerhub-pat: ${{ secrets.DOCKERHUB_TOKEN }}
          app-version: ${{ needs.setup.outputs.app-version }}
          api-base: ${{ vars.API_BASE_URL }}
          mi-base-url-path: ${{ vars.MARKET_INTELLIGENCE_BASE_URL_PATH }}

  migrate:
    name: Run Database Migrations
    needs: [setup, build]
    runs-on: ubuntu-latest
    environment: production
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - uses: ./.github/actions/setup-aws
        with:
          aws-region: ${{ vars.AWS_REGION }}
          aws-account-id: ${{ secrets.AWS_ACCOUNT_ID }}
          environment: production

      - name: Run API service migration
        uses: ./.github/actions/run-migration
        with:
          service-name: "api-service"
          cluster-name: "swift-production-cluster"
          task-definition-family: "swift-production-api-service-migration"
          subnets: "${{ needs.setup.outputs.private-subnet-ids }}"
          security-groups: "${{ needs.setup.outputs.api-service-security-group-id }}"
          aws-region: ${{ vars.AWS_REGION }}
          timeout-minutes: "15"

  deploy:
    name: Deploy Services
    needs: [setup, build, migrate]
    runs-on: ubuntu-latest
    environment: production
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - uses: ./.github/actions/prepare

      - name: Install Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.6.2

      - uses: ./.github/actions/setup-aws
        with:
          aws-region: ${{ vars.AWS_REGION }}
          aws-account-id: ${{ secrets.AWS_ACCOUNT_ID }}
          environment: production

      - name: Deploy infrastructure with shared cluster
        run: pnpm nx run infrastructure:deploy-shared-cluster:production
        env:
          AWS_REGION: ${{ vars.AWS_REGION }}
          AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
          IMAGE_TAG: ${{ needs.setup.outputs.app-version }}
          API_BASE_URL: ${{ vars.API_BASE_URL }}
          MARKET_INTELLIGENCE_BASE_URL_PATH: ${{ vars.MARKET_INTELLIGENCE_BASE_URL_PATH }}

  verify:
    name: Verify ${{ matrix.app-name }}
    needs: [setup, migrate, deploy]
    runs-on: ubuntu-latest
    environment: production
    permissions:
      id-token: write
      contents: read
    strategy:
      matrix: ${{ fromJson(needs.setup.outputs.matrix) }}
      fail-fast: false
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - uses: ./.github/actions/prepare

      - uses: ./.github/actions/setup-aws
        with:
          aws-region: ${{ vars.AWS_REGION }}
          aws-account-id: ${{ secrets.AWS_ACCOUNT_ID }}
          environment: production

      - name: Verify ${{ matrix.app-name }} deployment
        env:
          AWS_REGION: ${{ vars.AWS_REGION }}
        run: |
          echo "Verifying ${{ matrix.app-name }} deployment..."
          ./.github/scripts/validate-ecs-deployment.sh \
            "swift-production-cluster" \
            "swift-production-${{ matrix.app-name }}-service" \
            "${{ needs.setup.outputs.app-version }}"
