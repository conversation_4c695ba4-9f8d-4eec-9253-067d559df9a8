import { z } from "zod";

export const partSchema = z.object({
  id: z.number(),
  name: z.string(),
  commodityId: z.coerce.number().nullish(),
  subCommodityId: z.coerce.number().nullish(),
  subSubCommodityId: z.coerce.number().nullish(),
  commodity: z.string().nullish(),
  subCommodity: z.string().nullish(),
  subSubCommodity: z.string().nullish(),
  pl: z.string().nullish(),
  manufacturer: z.string().nullish(),
  package: z.string().nullish(),
  grade: z.string().nullish(),
});

export type Part = z.infer<typeof partSchema>;
