import { NextResponse } from "next/server";
import { createZodRoute } from "next-zod-route";
import { z } from "zod";

import { handleApiError } from "@/lib/api-error-handler";
import { prisma } from "@/lib/prisma";
import { partSchema } from "@/lib/schemas";

import { Prisma } from "../../../generated/client";

const searchParamsSchema = z
  .object({
    id: z.coerce.number().optional(),
    query: z.string().optional(),
  })
  .refine((data) => data.id || data.query, {
    message: "Either id or query must be provided",
    path: ["id", "query"],
  });

const buildPartFilters = ({
  id,
  query,
}: z.infer<typeof searchParamsSchema>) => {
  const baseFilter: Prisma.PartWhereInput = {
    ...(id && { partId: id }),
    ...(query && {
      partNumber: { contains: query, mode: "insensitive" },
    }),
  };

  return baseFilter;
};

export const GET = createZodRoute()
  .query(searchParamsSchema)
  .handler(async (request, context) => {
    try {
      const { id } = context.query;

      const where = buildPartFilters({
        id,
      });

      const part = await prisma.part.findFirstOrThrow({
        relationLoadStrategy: "query",
        select: {
          partId: true,
          partNumber: true,
          commodityId: true,
          subCommodityId: true,
          subSubCommodityId: true,
          commodity: {
            select: {
              name: true,
            },
          },
          subCommodity: {
            select: {
              name: true,
            },
          },
          subSubCommodity: {
            select: {
              name: true,
            },
          },
          grade: {
            select: {
              name: true,
            },
          },
          manufacturer: {
            select: {
              name: true,
            },
          },
          package: {
            select: {
              name: true,
            },
          },
          productLine: {
            select: {
              name: true,
            },
          },
        },
        where,
      });

      const transformedPart = {
        id: Number(part.partId),
        name: part.partNumber,
        commodityId: part.commodityId,
        subCommodityId: part.subCommodityId,
        subSubCommodityId: part.subSubCommodityId,
        commodity: part.commodity?.name ?? null,
        subCommodity: part.subCommodity?.name ?? null,
        subSubCommodity: part.subSubCommodity?.name ?? null,
        pl: part.productLine?.name ?? null,
        manufacturer: part.manufacturer?.name ?? null,
        grade: part.grade?.name ?? null,
        package: part.package?.name ?? null,
      };

      return NextResponse.json(partSchema.parse(transformedPart));
    } catch (error) {
      return handleApiError(error, {
        query: context.query,
        resourceId: context.query.id,
        resourceName: "part",
      });
    }
  });
