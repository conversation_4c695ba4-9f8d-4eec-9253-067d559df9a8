import { prisma } from "@/lib/prisma";
import { trendDataSchema } from "@/lib/schemas";

import { Prisma } from "../../../../generated/client";
import { type SearchParams } from "../schemas/input";

const buildPrismaWhere = (
  { commodity_id, subcommodity_id, subsubcommodity_id }: SearchParams,
  isPrice: boolean,
) => {
  const baseFilter: Prisma.CommodityTrendsMonthlyWhereInput = {
    commodityId: commodity_id,
    subCommodityId: subcommodity_id ?? Prisma.skip,
    subSubCommodityId: subsubcommodity_id ?? Prisma.skip,
  };
  if (!isPrice) {
    baseFilter.month = {
      gte: new Date("2023-01-01"),
    };
  }
  return baseFilter;
};

export const loadCommodityTrendData = async (params: SearchParams) => {
  const isPrice = params.target_attribute.includes("Price");
  const where = buildPrismaWhere(params, isPrice);

  const trendData = await prisma.commodityTrendsMonthly.findMany({
    where,
    select: {
      [params.target_attribute]: true,
      month: true,
      partCount: true,
    },
    orderBy: {
      month: "asc",
    },
  });

  const transformedTrendData = trendData.map((row) => ({
    week: row.month,
    value: row[params.target_attribute],
    number_of_parts: row.partCount,
    number_of_sellers: 0,
  }));

  return trendDataSchema.array().parse(transformedTrendData);
};
