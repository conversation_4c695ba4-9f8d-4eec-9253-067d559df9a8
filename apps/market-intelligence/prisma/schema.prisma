generator client {
  provider        = "prisma-client-js"
  output          = "../src/generated/client"
  previewFeatures = ["relationJoins", "strictUndefinedChecks"]
  binaryTargets   = ["native", "debian-openssl-3.0.x", "debian-openssl-1.1.x", "darwin-arm64", "linux-arm64-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("MARKET_INTELLIGENCE_DATABASE_URL")
}

model Part {
  partId              Int      @id(map: "pk_part_part_id") @map("part_id")
  partNumber          String?  @map("part_number")
  commodityId         BigInt?  @map("commodity_id")
  subCommodityId      BigInt?  @map("subcommodity_id")
  subSubCommodityId   BigInt?  @map("subsubcommodity_id")
  productLineId       Int?     @map("pl_id")
  manufacturerId      Int?     @map("manufacturer_id")
  gradeId             Int?     @map("grade_id")
  packageId           Int?     @map("package_id")
  processingTimestamp DateTime @map("processing_timestamp") @db.Timestamp(6)

  commodity       Commodity?    @relation("CommodityParts", fields: [commodityId], references: [id])
  subCommodity    Commodity?    @relation("SubCommodityParts", fields: [subCommodityId], references: [id])
  subSubCommodity Commodity?    @relation("SubSubCommodityParts", fields: [subSubCommodityId], references: [id])
  grade           Grade?        @relation(fields: [gradeId], references: [id])
  package         Package?      @relation(fields: [packageId], references: [id])
  productLine     ProductLine?  @relation(fields: [productLineId], references: [id])
  manufacturer    Manufacturer? @relation(fields: [manufacturerId], references: [id])

  @@index([partId], map: "idx_part_part_id")
  @@map("part")
}

model Commodity {
  id                  BigInt    @id(map: "pk_dim_commodity_id")
  level               BigInt?
  name                String
  parentId            BigInt?   @map("parent_id")
  processingTimestamp DateTime? @map("processing_timestamp") @db.Timestamp(6)

  commodityFilterOptions       FilterOptions[] @relation("CommodityFilterOptions")
  subCommodityFilterOptions    FilterOptions[] @relation("SubCommodityFilterOptions")
  subSubCommodityFilterOptions FilterOptions[] @relation("SubSubCommodityFilterOptions")
  commodityParts               Part[]          @relation("CommodityParts")
  subCommodityParts            Part[]          @relation("SubCommodityParts")
  subSubCommodityParts         Part[]          @relation("SubSubCommodityParts")

  @@index([name], map: "idx_dim_commodity_name")
  @@map("dim_commodity")
}

model Grade {
  id                  Int       @id(map: "pk_dim_grade_id")
  name                String
  processingTimestamp DateTime? @map("processing_timestamp") @db.Timestamp(6)

  part          Part[]
  filterOptions FilterOptions[] @relation("GradeFilterOptions")

  @@index([name], map: "idx_dim_grade_name")
  @@map("dim_grade")
}

model ProductLine {
  id                  Int       @id(map: "pk_dim_pl_id")
  name                String
  processingTimestamp DateTime? @map("processing_timestamp") @db.Timestamp(6)

  part          Part[]
  filterOptions FilterOptions[] @relation("ProductLineFilterOptions")

  @@index([name], map: "idx_dim_pl_name")
  @@map("dim_pl")
}

model Manufacturer {
  id                  Int       @id(map: "pk_dim_manufacturer_id")
  name                String
  processingTimestamp DateTime? @map("processing_timestamp") @db.Timestamp(6)

  part          Part[]
  filterOptions FilterOptions[] @relation("ManufacturerFilterOptions")

  @@index([name], map: "idx_dim_manufacturer_name")
  @@map("dim_manufacturer")
}

model Package {
  id                  Int       @id(map: "pk_dim_package_id")
  name                String
  processingTimestamp DateTime? @map("processing_timestamp") @db.Timestamp(6)

  part          Part[]
  filterOptions FilterOptions[] @relation("PackageFilterOptions")

  @@index([name], map: "idx_dim_package_name")
  @@map("dim_package")
}

model FilterOptions {
  id                  BigInt   @id @default(autoincrement())
  commodityId         BigInt?  @map("commodity_id")
  subCommodityId      BigInt?  @map("subcommodity_id")
  subSubCommodityId   BigInt?  @map("subsubcommodity_id")
  productLineId       Int?     @map("pl_id")
  manufacturerId      Int?     @map("manufacturer_id")
  gradeId             Int?     @map("grade_id")
  packageId           Int?     @map("package_id")
  productCount        BigInt?  @map("product_count")
  processingTimestamp DateTime @map("processing_timestamp") @db.Timestamp(6)

  commodity       Commodity?    @relation("CommodityFilterOptions", fields: [commodityId], references: [id])
  subCommodity    Commodity?    @relation("SubCommodityFilterOptions", fields: [subCommodityId], references: [id])
  subSubCommodity Commodity?    @relation("SubSubCommodityFilterOptions", fields: [subSubCommodityId], references: [id])
  productLine     ProductLine?  @relation("ProductLineFilterOptions", fields: [productLineId], references: [id])
  manufacturer    Manufacturer? @relation("ManufacturerFilterOptions", fields: [manufacturerId], references: [id])
  grade           Grade?        @relation("GradeFilterOptions", fields: [gradeId], references: [id])
  package         Package?      @relation("PackageFilterOptions", fields: [packageId], references: [id])

  @@index([commodityId], map: "idx_filter_options_commodity")
  @@index([commodityId, subCommodityId, subSubCommodityId, productLineId], map: "idx_filter_options_composite")
  @@index([manufacturerId], map: "idx_filter_options_manufacturer")
  @@index([productLineId], map: "idx_filter_options_pl")
  @@map("filter_options")
}

model CommodityTrendsMonthly {
  id                  String    @id(map: "pk_commodity_trends_monthly") @db.Uuid
  commodityId         BigInt?   @map("commodity_id")
  subCommodityId      BigInt?   @map("subcommodity_id")
  subSubCommodityId   BigInt?   @map("subsubcommodity_id")
  month               DateTime? @db.Date
  partCount           BigInt?   @map("part_count")
  avgPrice1           Decimal?  @map("avg_price_1") @db.Decimal(12, 2)
  minPrice1           Decimal?  @map("min_price_1") @db.Decimal(12, 2)
  avgPrice10          Decimal?  @map("avg_price_10") @db.Decimal(12, 2)
  minPrice10          Decimal?  @map("min_price_10") @db.Decimal(12, 2)
  avgPrice100         Decimal?  @map("avg_price_100") @db.Decimal(12, 2)
  minPrice100         Decimal?  @map("min_price_100") @db.Decimal(12, 2)
  avgPrice1000        Decimal?  @map("avg_price_1000") @db.Decimal(12, 2)
  minPrice1000        Decimal?  @map("min_price_1000") @db.Decimal(12, 2)
  avgPrice10000       Decimal?  @map("avg_price_10000") @db.Decimal(12, 2)
  minPrice10000       Decimal?  @map("min_price_10000") @db.Decimal(12, 2)
  avgPrice100000      Decimal?  @map("avg_price_100000") @db.Decimal(12, 2)
  minPrice100000      Decimal?  @map("min_price_100000") @db.Decimal(12, 2)
  avgPrice1000000     Decimal?  @map("avg_price_1000000") @db.Decimal(12, 2)
  minPrice1000000     Decimal?  @map("min_price_1000000") @db.Decimal(12, 2)
  avgStock            Decimal?  @map("avg_stock") @db.Decimal(12, 2)
  minStock            Decimal?  @map("min_stock") @db.Decimal(12, 2)
  avgLeadTime         Decimal?  @map("avg_lead_time") @db.Decimal(12, 2)
  minLeadTime         Decimal?  @map("min_lead_time") @db.Decimal(12, 2)
  processingTimestamp DateTime  @map("processing_timestamp") @db.Timestamp(6)

  @@index([commodityId, subCommodityId, subSubCommodityId, month], map: "idx_commodity_trends_monthly_composite")
  @@index([month], map: "idx_commodity_trends_monthly_time")
  @@map("commodity_trends_monthly")
}

model CommodityTrendsWeekly {
  id                  String    @id(map: "pk_commodity_trends_weekly") @db.Uuid
  commodityId         BigInt?   @map("commodity_id")
  subCommodityId      BigInt?   @map("subcommodity_id")
  subSubCommodityId   BigInt?   @map("subsubcommodity_id")
  week                DateTime? @db.Date
  partCount           BigInt?   @map("part_count")
  avgPrice1           Decimal?  @map("avg_price_1") @db.Decimal(12, 2)
  minPrice1           Decimal?  @map("min_price_1") @db.Decimal(12, 2)
  avgPrice10          Decimal?  @map("avg_price_10") @db.Decimal(12, 2)
  minPrice10          Decimal?  @map("min_price_10") @db.Decimal(12, 2)
  avgPrice100         Decimal?  @map("avg_price_100") @db.Decimal(12, 2)
  minPrice100         Decimal?  @map("min_price_100") @db.Decimal(12, 2)
  avgPrice1000        Decimal?  @map("avg_price_1000") @db.Decimal(12, 2)
  minPrice1000        Decimal?  @map("min_price_1000") @db.Decimal(12, 2)
  avgPrice10000       Decimal?  @map("avg_price_10000") @db.Decimal(12, 2)
  minPrice10000       Decimal?  @map("min_price_10000") @db.Decimal(12, 2)
  avgPrice100000      Decimal?  @map("avg_price_100000") @db.Decimal(12, 2)
  minPrice100000      Decimal?  @map("min_price_100000") @db.Decimal(12, 2)
  avgPrice1000000     Decimal?  @map("avg_price_1000000") @db.Decimal(12, 2)
  minPrice1000000     Decimal?  @map("min_price_1000000") @db.Decimal(12, 2)
  avgStock            Decimal?  @map("avg_stock") @db.Decimal(12, 2)
  minStock            Decimal?  @map("min_stock") @db.Decimal(12, 2)
  avgLeadTime         Decimal?  @map("avg_lead_time") @db.Decimal(12, 2)
  minLeadTime         Decimal?  @map("min_lead_time") @db.Decimal(12, 2)
  processingTimestamp DateTime  @map("processing_timestamp") @db.Timestamp(6)

  @@index([commodityId, subCommodityId, subSubCommodityId, week], map: "idx_commodity_trends_weekly_composite")
  @@index([week], map: "idx_commodity_trends_weekly_time")
  @@map("commodity_trends_weekly")
}

model PartSellerTrendsMonthly {
  id                  String    @id(map: "pk_part_seller_trends_monthly")
  partId              String?   @map("part_id")
  sellerId            String?   @map("seller_id")
  commodityId         BigInt?   @map("commodity_id")
  subCommodityId      BigInt?   @map("subcommodity_id")
  subSubCommodityId   BigInt?   @map("subsubcommodity_id")
  gradeId             Int?      @map("grade_id")
  packageId           Int?      @map("package_id")
  productLineId       Int?      @map("pl_id")
  manufacturerId      Int?      @map("manufacturer_id")
  month               DateTime? @db.Date
  avgPrice1           Decimal?  @map("avg_price_1") @db.Decimal(12, 2)
  minPrice1           Decimal?  @map("min_price_1") @db.Decimal(12, 2)
  avgPrice10          Decimal?  @map("avg_price_10") @db.Decimal(12, 2)
  minPrice10          Decimal?  @map("min_price_10") @db.Decimal(12, 2)
  avgPrice100         Decimal?  @map("avg_price_100") @db.Decimal(12, 2)
  minPrice100         Decimal?  @map("min_price_100") @db.Decimal(12, 2)
  avgPrice1000        Decimal?  @map("avg_price_1000") @db.Decimal(12, 2)
  minPrice1000        Decimal?  @map("min_price_1000") @db.Decimal(12, 2)
  avgPrice10000       Decimal?  @map("avg_price_10000") @db.Decimal(12, 2)
  minPrice10000       Decimal?  @map("min_price_10000") @db.Decimal(12, 2)
  avgPrice100000      Decimal?  @map("avg_price_100000") @db.Decimal(12, 2)
  minPrice100000      Decimal?  @map("min_price_100000") @db.Decimal(12, 2)
  avgPrice1000000     Decimal?  @map("avg_price_1000000") @db.Decimal(12, 2)
  minPrice1000000     Decimal?  @map("min_price_1000000") @db.Decimal(12, 2)
  avgStock            Decimal?  @map("avg_stock") @db.Decimal(12, 2)
  minStock            Decimal?  @map("min_stock") @db.Decimal(12, 2)
  avgLeadTime         Decimal?  @map("avg_lead_time") @db.Decimal(12, 2)
  minLeadTime         Decimal?  @map("min_lead_time") @db.Decimal(12, 2)
  processingTimestamp DateTime  @map("processing_timestamp") @db.Timestamp(6)

  @@index([commodityId, subCommodityId, subSubCommodityId, month], map: "idx_part_seller_trends_monthly_commodity_time")
  @@index([commodityId, subCommodityId, subSubCommodityId, gradeId, packageId, productLineId, manufacturerId], map: "idx_part_seller_trends_monthly_hierarchy")
  @@index([partId, sellerId, month], map: "idx_part_seller_trends_monthly_part_seller")
  @@index([partId, month], map: "idx_part_seller_trends_monthly_part_time")
  @@index([month], map: "idx_part_seller_trends_monthly_time")
  @@map("part_seller_trends_monthly")
}

model PartSellerTrendsWeekly {
  id                  String    @id(map: "pk_part_seller_trends_weekly")
  partId              String?   @map("part_id")
  sellerId            String?   @map("seller_id")
  commodityId         BigInt?   @map("commodity_id")
  subCommodityId      BigInt?   @map("subcommodity_id")
  subSubCommodityId   BigInt?   @map("subsubcommodity_id")
  gradeId             Int?      @map("grade_id")
  packageId           Int?      @map("package_id")
  productLineId       Int?      @map("pl_id")
  manufacturerId      Int?      @map("manufacturer_id")
  week                DateTime? @db.Date
  avgPrice1           Decimal?  @map("avg_price_1") @db.Decimal(12, 2)
  minPrice1           Decimal?  @map("min_price_1") @db.Decimal(12, 2)
  avgPrice10          Decimal?  @map("avg_price_10") @db.Decimal(12, 2)
  minPrice10          Decimal?  @map("min_price_10") @db.Decimal(12, 2)
  avgPrice100         Decimal?  @map("avg_price_100") @db.Decimal(12, 2)
  minPrice100         Decimal?  @map("min_price_100") @db.Decimal(12, 2)
  avgPrice1000        Decimal?  @map("avg_price_1000") @db.Decimal(12, 2)
  minPrice1000        Decimal?  @map("min_price_1000") @db.Decimal(12, 2)
  avgPrice10000       Decimal?  @map("avg_price_10000") @db.Decimal(12, 2)
  minPrice10000       Decimal?  @map("min_price_10000") @db.Decimal(12, 2)
  avgPrice100000      Decimal?  @map("avg_price_100000") @db.Decimal(12, 2)
  minPrice100000      Decimal?  @map("min_price_100000") @db.Decimal(12, 2)
  avgPrice1000000     Decimal?  @map("avg_price_1000000") @db.Decimal(12, 2)
  minPrice1000000     Decimal?  @map("min_price_1000000") @db.Decimal(12, 2)
  avgStock            Decimal?  @map("avg_stock") @db.Decimal(12, 2)
  minStock            Decimal?  @map("min_stock") @db.Decimal(12, 2)
  avgLeadTime         Decimal?  @map("avg_lead_time") @db.Decimal(12, 2)
  minLeadTime         Decimal?  @map("min_lead_time") @db.Decimal(12, 2)
  processingTimestamp DateTime  @map("processing_timestamp") @db.Timestamp(6)

  @@index([commodityId, subCommodityId, subSubCommodityId, week], map: "idx_part_seller_trends_weekly_commodity_time")
  @@index([commodityId, subCommodityId, subSubCommodityId, gradeId, packageId, productLineId, manufacturerId], map: "idx_part_seller_trends_weekly_hierarchy")
  @@index([partId, sellerId, week], map: "idx_part_seller_trends_weekly_part_seller")
  @@index([partId, week], map: "idx_part_seller_trends_weekly_part_time")
  @@index([week], map: "idx_part_seller_trends_weekly_time")
  @@map("part_seller_trends_weekly")
}

model PartTrendsMonthly {
  id                  String    @id(map: "pk_part_trends_monthly")
  partId              String?   @map("part_id")
  sellerId            BigInt?   @map("seller_id")
  commodityId         BigInt?   @map("commodity_id")
  subCommodityId      BigInt?   @map("subcommodity_id")
  subSubCommodityId   BigInt?   @map("subsubcommodity_id")
  gradeId             Int?      @map("grade_id")
  packageId           Int?      @map("package_id")
  productLineId       Int?      @map("pl_id")
  manufacturerId      Int?      @map("manufacturer_id")
  sellerCount         BigInt?   @map("seller_count")
  month               DateTime? @db.Date
  avgPrice1           Decimal?  @map("avg_price_1") @db.Decimal(12, 2)
  minPrice1           Decimal?  @map("min_price_1") @db.Decimal(12, 2)
  avgPrice10          Decimal?  @map("avg_price_10") @db.Decimal(12, 2)
  minPrice10          Decimal?  @map("min_price_10") @db.Decimal(12, 2)
  avgPrice100         Decimal?  @map("avg_price_100") @db.Decimal(12, 2)
  minPrice100         Decimal?  @map("min_price_100") @db.Decimal(12, 2)
  avgPrice1000        Decimal?  @map("avg_price_1000") @db.Decimal(12, 2)
  minPrice1000        Decimal?  @map("min_price_1000") @db.Decimal(12, 2)
  avgPrice10000       Decimal?  @map("avg_price_10000") @db.Decimal(12, 2)
  minPrice10000       Decimal?  @map("min_price_10000") @db.Decimal(12, 2)
  avgPrice100000      Decimal?  @map("avg_price_100000") @db.Decimal(12, 2)
  minPrice100000      Decimal?  @map("min_price_100000") @db.Decimal(12, 2)
  avgPrice1000000     Decimal?  @map("avg_price_1000000") @db.Decimal(12, 2)
  minPrice1000000     Decimal?  @map("min_price_1000000") @db.Decimal(12, 2)
  avgStock            Decimal?  @map("avg_stock") @db.Decimal(12, 2)
  minStock            Decimal?  @map("min_stock") @db.Decimal(12, 2)
  avgLeadTime         Decimal?  @map("avg_lead_time") @db.Decimal(12, 2)
  minLeadTime         Decimal?  @map("min_lead_time") @db.Decimal(12, 2)
  processingTimestamp DateTime  @map("processing_timestamp") @db.Timestamp(6)

  @@index([commodityId, subCommodityId, subSubCommodityId, month], map: "idx_part_trends_monthly_commodity_time")
  @@index([commodityId, subCommodityId, subSubCommodityId, gradeId, packageId, productLineId, manufacturerId], map: "idx_part_trends_monthly_hierarchy")
  @@index([partId, month], map: "idx_part_trends_monthly_part_time")
  @@index([month], map: "idx_part_trends_monthly_time")
  @@map("part_trends_monthly")
}

model PartTrendsWeekly {
  id                  String    @id(map: "pk_part_trends_weekly")
  partId              String?   @map("part_id")
  sellerId            BigInt?   @map("seller_id")
  commodityId         BigInt?   @map("commodity_id")
  subCommodityId      BigInt?   @map("subcommodity_id")
  subSubCommodityId   BigInt?   @map("subsubcommodity_id")
  gradeId             Int?      @map("grade_id")
  packageId           Int?      @map("package_id")
  productLineId       Int?      @map("pl_id")
  manufacturerId      Int?      @map("manufacturer_id")
  sellerCount         BigInt?   @map("seller_count")
  week                DateTime? @db.Date
  avgPrice1           Decimal?  @map("avg_price_1") @db.Decimal(12, 2)
  minPrice1           Decimal?  @map("min_price_1") @db.Decimal(12, 2)
  avgPrice10          Decimal?  @map("avg_price_10") @db.Decimal(12, 2)
  minPrice10          Decimal?  @map("min_price_10") @db.Decimal(12, 2)
  avgPrice100         Decimal?  @map("avg_price_100") @db.Decimal(12, 2)
  minPrice100         Decimal?  @map("min_price_100") @db.Decimal(12, 2)
  avgPrice1000        Decimal?  @map("avg_price_1000") @db.Decimal(12, 2)
  minPrice1000        Decimal?  @map("min_price_1000") @db.Decimal(12, 2)
  avgPrice10000       Decimal?  @map("avg_price_10000") @db.Decimal(12, 2)
  minPrice10000       Decimal?  @map("min_price_10000") @db.Decimal(12, 2)
  avgPrice100000      Decimal?  @map("avg_price_100000") @db.Decimal(12, 2)
  minPrice100000      Decimal?  @map("min_price_100000") @db.Decimal(12, 2)
  avgPrice1000000     Decimal?  @map("avg_price_1000000") @db.Decimal(12, 2)
  minPrice1000000     Decimal?  @map("min_price_1000000") @db.Decimal(12, 2)
  avgStock            Decimal?  @map("avg_stock") @db.Decimal(12, 2)
  minStock            Decimal?  @map("min_stock") @db.Decimal(12, 2)
  avgLeadTime         Decimal?  @map("avg_lead_time") @db.Decimal(12, 2)
  minLeadTime         Decimal?  @map("min_lead_time") @db.Decimal(12, 2)
  processingTimestamp DateTime  @map("processing_timestamp") @db.Timestamp(6)

  @@index([commodityId, subCommodityId, subSubCommodityId, week], map: "idx_part_trends_weekly_commodity_time")
  @@index([commodityId, subCommodityId, subSubCommodityId, gradeId, packageId, productLineId, manufacturerId], map: "idx_part_trends_weekly_hierarchy")
  @@index([partId, week], map: "idx_part_trends_weekly_part_time")
  @@index([week], map: "idx_part_trends_weekly_time")
  @@map("part_trends_weekly")
}
