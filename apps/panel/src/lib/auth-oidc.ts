"use client";

import { User, User<PERSON>anager, WebStorageStateStore } from "oidc-client-ts";

export const getEnv = () => {
  if (typeof window === "undefined") {
    return {
      ssoAuthority:
        process.env.NEXT_PUBLIC_SSO_AUTHORITY || "https://testsso.z2data.com",
      ssoClientId: process.env.NEXT_PUBLIC_SSO_CLIENT_ID || "renderinserver",
      baseUrl: "http://localhost:4200",
    };
  }

  const hostname = window.location.hostname;
  const protocol = window.location.protocol;
  const port = window.location.port;
  const baseUrl = `${protocol}//${hostname}${port ? `:${port}` : ""}`;

  // Localhost environment
  if (hostname === "localhost") {
    return {
      ssoAuthority: "https://testsso.z2data.com",
      ssoClientId: "97f572a6-f6f5-43c5-ad24-7eac57b7088d",
      baseUrl,
    };
  }

  // Test/staging environment
  if (hostname === "rfq-dev.int.ztwo.app") {
    return {
      ssoAuthority: "https://testsso.z2data.com",
      ssoClientId: "c34d2b64-5c08-455a-984b-3b944d67b782",
      baseUrl,
    };
  }

  // Production environment
  if (hostname === "rfq-plus.int.ztwo.app") {
    return {
      ssoAuthority: "https://testsso.z2data.com",
      ssoClientId: "76e63534-0040-4b3f-a228-ebba17bce382",
      baseUrl,
    };
  }

  return {
    ssoAuthority: "https://testsso.z2data.com",
    ssoClientId: "missinghostname",
    baseUrl,
  };
};

const getConfig = () => {
  const env = getEnv();

  return {
    authority: env.ssoAuthority,
    automaticSilentRenew: true,
    client_id: env.ssoClientId,
    post_logout_redirect_uri: env.baseUrl,
    redirect_uri: `${env.baseUrl}/auth-callback`,
    response_type: "code",
    scope: "openid profile email api.read",
    silent_redirect_uri: `${env.baseUrl}/silent-renew`,
    userStore:
      typeof window !== "undefined"
        ? new WebStorageStateStore({ store: window.localStorage })
        : undefined,
  };
};

export const createUserManager = () => {
  if (typeof window === "undefined") {
    return null;
  }
  return new UserManager(getConfig());
};

export const userManager = createUserManager();

export const loginWithRedirect = async (returnUrl = "/") => {
  if (!userManager) return;
  await userManager.signinRedirect({ state: returnUrl });
};

export const logoutWithRedirect = async () => {
  if (!userManager) return;
  await userManager.removeUser();

  // Perform logout redirect without id_token_hint
  const env = getEnv();
  const logoutUrl = `${env.ssoAuthority}/connect/endsession?post_logout_redirect_uri=${encodeURIComponent(env.baseUrl)}`;
  window.location.href = logoutUrl;
};

export const getCurrentUser = async (): Promise<User | null> => {
  if (!userManager) return null;
  try {
    const user = await userManager.getUser();
    return user;
  } catch (error) {
    console.error("Error getting user:", error);
    return null;
  }
};

export const handleAuthCallback = async () => {
  if (!userManager) return null;
  try {
    const user = await userManager.signinRedirectCallback();
    if (user && user.state) {
      window.location.href = user.state as string;
    }
    return user;
  } catch (error) {
    console.error("Error handling auth callback:", error);
    throw error;
  }
};

export const handleSilentRenew = async () => {
  if (!userManager) return null;
  try {
    const user = await userManager.signinSilentCallback();
    return user;
  } catch (error) {
    console.error("Error handling silent renew:", error);
    throw error;
  }
};

export const isAuthenticated = async (): Promise<boolean> => {
  const user = await getCurrentUser();
  return !!user && !user.expired;
};

export const getAccessToken = async (): Promise<string | null> => {
  const user = await getCurrentUser();
  return user?.access_token || null;
};

export const getUserProfile = async () => {
  const user = await getCurrentUser();
  if (!user) return null;

  const profile = user.profile;
  const role = Array.isArray(profile.role) ? profile.role : ["FREE"];

  return {
    accessToken: user.access_token,
    email: profile.email as string,
    id: profile.sub,
    idToken: user.id_token,
    name: profile.name as string,
    role,
    ...profile,
  };
};
