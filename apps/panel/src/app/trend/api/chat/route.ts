import { openai } from "@ai-sdk/openai";
import { streamText } from "ai";

//Allow streaming responses up to 30 seconds
export const maxDuration = 30;

export async function POST(req: Request) {
  const { messages, prompt } = await req.json();

  let result;

  if (prompt) {
    result = streamText({
      model: openai.responses("gpt-4o"),
      prompt,
      providerOptions: {
        openai: {
          reasoningEffort: "hight",
        },
      },
    });
  } else {
    result = streamText({
      messages,
      model: openai("gpt-4o"),
      system: "You are a helpful market intelligence assistant.",
    });
  }

  return result.toDataStreamResponse();
}
