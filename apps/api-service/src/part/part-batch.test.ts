import { Test, TestingModule } from '@nestjs/testing';
import { PartService } from './part.service';
import { PartRepository } from './part.repo';
import { PartAlternativeRepo } from './part-alternative.repo';
import { Z2DataService } from '../z2data/z2data.service';
import { ManufacturerNormalizationService } from '../manufacturer/manufacturer-normalization.service';
import { PrismaService } from '../prisma.service';
import { MatchedStatus } from '../bom/types';

describe('PartService Batch Operations', () => {
  let service: PartService;
  let partRepo: jest.Mocked<PartRepository>;
  let z2dataService: jest.Mocked<Z2DataService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PartService,
        {
          provide: PartRepository,
          useValue: {
            findById: jest.fn(),
            findByMpnAndManufacturer: jest.fn(),
            findOrCreateManufacturer: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: PartAlternativeRepo,
          useValue: {
            findCachedAlternatives: jest.fn(),
            upsertAlternatives: jest.fn(),
            findAlternativePartLocally: jest.fn(),
          },
        },
        {
          provide: Z2DataService,
          useValue: {
            getCrossDataByPartId: jest.fn(),
            validateParts: jest.fn(),
          },
        },
        {
          provide: ManufacturerNormalizationService,
          useValue: {
            normalizeManufacturer: jest.fn(),
          },
        },
        {
          provide: PrismaService,
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<PartService>(PartService);
    partRepo = module.get(PartRepository);
    z2dataService = module.get(Z2DataService);
  });

  describe('batchFindPartsByMpnAndManufacturer', () => {
    it('should process parts in batches and return results in original order', async () => {
      // Arrange
      const partRequests = [
        { mpn: 'PART001', manufacturer_name: 'Manufacturer A', originalIndex: 0 },
        { mpn: 'PART002', manufacturer_name: 'Manufacturer B', originalIndex: 1 },
        { mpn: 'PART003', manufacturer_name: undefined, originalIndex: 2 },
      ];

      const mockLocalPart = {
        id: 1,
        mpn: 'PART001',
        manufacturer: { id: 1, name: 'Manufacturer A' },
      } as any;

      const mockZ2ValidationResponse = {
        statusCode: 200,
        results: [
          {
            rowNumber: 1,
            matchStatus: 'Exact',
            matchReason: 'Exact match found',
            z2PartData: {
              partID: 123,
              partNumber: 'PART002',
              companyName: 'Manufacturer B',
              companyID: 456,
              groupID: 789,
            },
          },
        ],
      };

      const mockCreatedPart = {
        id: 2,
        mpn: 'PART002',
        z2_part_id: 123,
      } as any;

      // Mock the findByMpnAndManufacturer method for local lookups
      partRepo.findByMpnAndManufacturer.mockImplementation(async (mpn, manufacturer) => {
        if (mpn === 'PART001' && manufacturer === 'Manufacturer A') {
          return mockLocalPart;
        }
        return null;
      });

      z2dataService.validateParts.mockResolvedValue(mockZ2ValidationResponse);

      // Mock the findOrCreatePartBy method
      jest.spyOn(service, 'findOrCreatePartBy' as any).mockResolvedValue(mockCreatedPart);

      // Act
      const results = await service.batchFindPartsByMpnAndManufacturer(partRequests);

      // Assert
      expect(results).toHaveLength(3);
      
      // First part should be found locally
      expect(results[0]).toEqual({
        part: mockLocalPart,
        matchedStatus: MatchedStatus.Matched,
        matchReason: 'Exact match by MPN and manufacturer',
        originalIndex: 0,
      });

      // Second part should be created from Z2Data
      expect(results[1]).toEqual({
        part: mockCreatedPart,
        matchedStatus: MatchedStatus.Matched,
        matchReason: 'Exact match found',
        originalIndex: 1,
      });

      // Third part should not be matched
      expect(results[2]).toEqual({
        part: null,
        matchedStatus: MatchedStatus.NotMatched,
        matchReason: 'No match found',
        originalIndex: 2,
      });

      // Verify Z2Data was called with correct batch (only unmatched parts)
      expect(z2dataService.validateParts).toHaveBeenCalledWith({
        rows: [
          { man: 'Manufacturer B', mpn: 'PART002', rowNumber: 1 },
          { man: '', mpn: 'PART003', rowNumber: 2 },
        ],
      });

      // Verify individual local lookups were called through the repository
      expect(partRepo.findByMpnAndManufacturer).toHaveBeenCalledWith('PART001', 'Manufacturer A');
      expect(partRepo.findByMpnAndManufacturer).toHaveBeenCalledWith('PART002', 'Manufacturer B');
    });

    it('should handle Z2Data API failure gracefully', async () => {
      // Arrange
      const partRequests = [
        { mpn: 'PART001', manufacturer_name: 'Manufacturer A', originalIndex: 0 },
      ];

      // Mock individual local lookup to return null
      partRepo.findByMpnAndManufacturer.mockResolvedValue(null);
      z2dataService.validateParts.mockResolvedValue(null); // API failure

      // Act
      const results = await service.batchFindPartsByMpnAndManufacturer(partRequests);

      // Assert
      expect(results).toHaveLength(1);
      expect(results[0]).toEqual({
        part: null,
        matchedStatus: MatchedStatus.NotMatched,
        matchReason: 'No match found',
        originalIndex: 0,
      });
    });

    it('should chunk large requests into batches of 500', async () => {
      // Arrange - Create 1000 part requests
      const partRequests = Array.from({ length: 1000 }, (_, i) => ({
        mpn: `PART${i.toString().padStart(3, '0')}`,
        manufacturer_name: `Manufacturer ${i}`,
        originalIndex: i,
      }));

      // Mock individual local lookups to return null (so all go to Z2Data)
      partRepo.findByMpnAndManufacturer.mockResolvedValue(null);
      z2dataService.validateParts.mockResolvedValue({
        statusCode: 200,
        results: [],
      });

      // Act
      const results = await service.batchFindPartsByMpnAndManufacturer(partRequests);

      // Assert
      expect(results).toHaveLength(1000);

      // Should be called twice (500 + 500) for Z2Data batching
      expect(z2dataService.validateParts).toHaveBeenCalledTimes(2);

      // First call should have 500 items
      expect(z2dataService.validateParts).toHaveBeenNthCalledWith(1, {
        rows: expect.arrayContaining([
          expect.objectContaining({ rowNumber: 1 }),
          expect.objectContaining({ rowNumber: 500 }),
        ]),
      });

      // Second call should have 500 items
      expect(z2dataService.validateParts).toHaveBeenNthCalledWith(2, {
        rows: expect.arrayContaining([
          expect.objectContaining({ rowNumber: 1 }),
          expect.objectContaining({ rowNumber: 500 }),
        ]),
      });

      // Individual local lookups should be called 1000 times through repository
      expect(partRepo.findByMpnAndManufacturer).toHaveBeenCalledTimes(1000);
    });
  });
});
