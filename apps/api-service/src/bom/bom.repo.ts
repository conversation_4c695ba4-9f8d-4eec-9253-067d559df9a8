import { Injectable } from "@nestjs/common";

import {
  bom as PrismaBom,
  bom_item as PrismaBomItem,
  Prisma,
} from "../generated/client";
import { PrismaService } from "../prisma.service";
import { CreateBomInput, CreateBomItemModel, UpdateBomInput } from "./types";

@Injectable()
export class BomRepository {
  constructor(private prisma: PrismaService) {}

  async create(data: CreateBomInput): Promise<PrismaBom> {
    return this.prisma.bom.create({
      data,
      include: {
        bom_items: true,
      },
    });
  }

  async findMany(
    params: {
      includeBomItems?: boolean;
      includeSupplies?: boolean;
    } = {},
  ): Promise<PrismaBom[]> {
    const { includeBomItems = false, includeSupplies = false } = params;

    return this.prisma.bom.findMany({
      include: {
        ...(includeBomItems
          ? {
              bom_items: {
                include: {
                  part: {
                    include: {
                      manufacturer: true,
                      ...(includeSupplies
                        ? { supplies: { include: { seller: true } } }
                        : {}),
                    },
                  },
                },
              },
            }
          : {}),
      },
    });
  }

  async findById(
    id: number,
    params: {
      includeBomItems?: boolean;
      includeSupplies?: boolean;
    } = {},
  ): Promise<PrismaBom | null> {
    const { includeBomItems = false, includeSupplies = false } = params;

    return this.prisma.bom.findUnique({
      include: {
        ...(includeBomItems
          ? {
              bom_items: {
                include: {
                  part: {
                    include: {
                      manufacturer: true,
                      ...(includeSupplies
                        ? {
                            supplies: {
                              include: { seller: true },
                              where: {
                                AND: [{ prices: { not: Prisma.DbNull } }],
                              },
                            },
                          }
                        : {}),
                    },
                  },
                  supply: { include: { seller: true } },
                },
              },
            }
          : {}),
      },
      where: { id },
    });
  }

  async update(
    id: number,
    data: Omit<UpdateBomInput, "id">,
  ): Promise<PrismaBom> {
    return this.prisma.bom.update({
      data,
      include: {
        bom_items: true,
      },
      where: { id },
    });
  }

  async delete(id: number): Promise<PrismaBom> {
    return this.prisma.bom.delete({
      include: {
        bom_items: true,
      },
      where: { id },
    });
  }

  async createBomItem(data: CreateBomItemModel): Promise<PrismaBomItem> {
    return this.prisma.bom_item.create({
      data: {
        idx: data.idx!,
        ...data,
      },
      include: {
        bom: true,
        part: { include: { manufacturer: true } },
        supply: { include: { seller: true } },
      },
    });
  }

  async batchCreateBomItems(bomItems: CreateBomItemModel[]): Promise<PrismaBomItem[]> {
    // Use a transaction to ensure all items are created atomically
    return this.prisma.$transaction(async (tx) => {
      const createdItems: PrismaBomItem[] = [];

      for (const item of bomItems) {
        const createdItem = await tx.bom_item.create({
          data: {
            idx: item.idx!,
            ...item,
          },
          include: {
            bom: true,
            part: { include: { manufacturer: true } },
            supply: { include: { seller: true } },
          },
        });
        createdItems.push(createdItem);
      }

      return createdItems;
    });
  }

  async findBomItems(bomId: number): Promise<PrismaBomItem[]> {
    return this.prisma.bom_item.findMany({
      include: {
        part: { include: { manufacturer: true } },
        supply: { include: { seller: true } },
      },
      orderBy: {
        idx: "asc",
      },
      where: {
        bom_id: bomId,
      },
    });
  }

  async findBomItemById(id: number): Promise<PrismaBomItem | null> {
    return this.prisma.bom_item.findUnique({
      include: {
        bom: true,
        part: { include: { manufacturer: true } },
        supply: { include: { seller: true } },
      },
      where: { id },
    });
  }

  async updateBomItem(
    id: number,
    data: Prisma.bom_itemUpdateInput,
  ): Promise<PrismaBomItem> {
    return this.prisma.bom_item.update({
      data,
      include: {
        bom: true,
        part: { include: { manufacturer: true } },
        supply: { include: { seller: true } },
      },
      where: { id },
    });
  }

  async deleteBomItem(id: number): Promise<PrismaBomItem> {
    return this.prisma.bom_item.delete({
      include: {
        bom: true,
        part: { include: { manufacturer: true } },
        supply: { include: { seller: true } },
      },
      where: { id },
    });
  }

  async findDistinctProjects(
    search?: string,
    limit: number = 5,
  ): Promise<{ project: string | null }[]> {
    return this.prisma.bom.findMany({
      distinct: ["project"],
      select: {
        project: true,
      },
      take: limit,
      where: {
        AND: [
          {
            project: {
              not: null,
            },
          },
          {
            project: {
              not: "",
            },
          },
          ...(search
            ? [
                {
                  project: {
                    contains: search,
                    mode: "insensitive" as const,
                  },
                },
              ]
            : []),
        ],
      },
    });
  }

  async findMaxIdxForBom(bomId: number): Promise<number> {
    const result = await this.prisma.bom_item.aggregate({
      _max: {
        idx: true,
      },
      where: { bom_id: bomId },
    });

    return result._max.idx || 0;
  }
}
