import { Injectable, Logger } from "@nestjs/common";

import { part } from "../generated/client";
import { PartService } from "../part/part.service";
import { SupplyService } from "../supply/supply.service";
import { BomRepository } from "./bom.repo";
import {
  Bom,
  BomItem,
  CreateBomInput,
  CreateBomItemInput,
  CreateBomItemModel,
  MatchedStatus,
  ParsedBomItem,
  UpdateBomInput,
  UpdateBomItemInput,
} from "./types";

@Injectable()
export class BomService {
  private readonly logger = new Logger(BomService.name);

  constructor(
    private readonly bomRepo: BomRepository,
    private readonly partService: PartService,
    private readonly supplyService: SupplyService,
  ) {}

  async createBom(createBomInput: CreateBomInput): Promise<Bom> {
    const sanitizedInput = this.sanitizeBomInput(createBomInput);
    return this.bomRepo.create(sanitizedInput) as unknown as Promise<Bom>;
  }

  async findAllBoms(
    includeBomItems: boolean = false,
    includeSupplies: boolean = false,
  ): Promise<Bom[]> {
    return this.bomRepo.findMany({
      includeBomItems,
      includeSupplies,
    }) as unknown as Promise<Bom[]>;
  }

  async findBomById(
    id: number,
    includeBomItems: boolean = false,
    includeSupplies: boolean = false,
  ): Promise<Bom | null> {
    return this.bomRepo.findById(id, {
      includeBomItems,
      includeSupplies,
    }) as unknown as Promise<Bom | null>;
  }

  async updateBom(updateBomInput: UpdateBomInput): Promise<Bom> {
    const { id, ...inputData } = updateBomInput;
    const sanitizedData = this.sanitizeBomInput(inputData);
    return this.bomRepo.update(id, sanitizedData) as unknown as Promise<Bom>;
  }

  async deleteBom(id: number): Promise<Bom> {
    return this.bomRepo.delete(id) as unknown as Promise<Bom>;
  }

  async createBomItem(
    createBomItemInput: CreateBomItemInput,
    processSupply: boolean,
  ): Promise<BomItem> {
    const [foundPart, matchedReason] =
      await this.partService.findPartByMpnAndManufacturer(
        createBomItemInput.mpn,
        createBomItemInput.manufacturer_name,
      );
    if (foundPart) {
      // Ensure part has fresh Z2Data (lifecycle and RoHS status)
      await this.partService.ensurePartHasFreshZ2Data(foundPart.id);

      const bestSupplyId: bigint | null = processSupply
        ? await this.supplyService.findBestSupplyIdBasedOnQuantity(
            foundPart.id,
            foundPart.mpn,
            createBomItemInput.quantity,
          )
        : null;

      return this.createAndSaveBomItem({
        bom_id: createBomItemInput.bom_id,
        idx: createBomItemInput.idx,
        manufacturer_name: createBomItemInput.manufacturer_name,
        match_reason: matchedReason,
        matched_part_id: foundPart.id,
        matched_status: MatchedStatus.Matched,
        mpn: createBomItemInput.mpn,
        quantity: createBomItemInput.quantity,
        supply_id: bestSupplyId,
      });
    }

    return this.createAndSaveBomItem({
      bom_id: createBomItemInput.bom_id,
      idx: createBomItemInput.idx,
      manufacturer_name: createBomItemInput.manufacturer_name,
      match_reason: matchedReason,
      matched_part_id: null,
      matched_status: MatchedStatus.NotMatched,
      mpn: createBomItemInput.mpn,
      quantity: createBomItemInput.quantity,
    });
  }

  async batchCreateBomItems(
    bomId: number,
    itemsWithParts: Array<{
      bomItem: ParsedBomItem;
      part: part | null;
      matchedStatus: MatchedStatus;
      matchReason: string;
      index: number;
    }>,
    maxItemsForSupplyProcessing: number
  ): Promise<BomItem[]> {
    this.logger.debug(`Batch creating ${itemsWithParts.length} BOM items`);

    // Step 1: Ensure all matched parts have fresh Z2Data
    const partsToUpdate = itemsWithParts
      .filter(item => item.part !== null)
      .map(item => item.part!.id);

    if (partsToUpdate.length > 0) {
      this.logger.debug(`Ensuring ${partsToUpdate.length} parts have fresh Z2Data`);
      await Promise.all(
        partsToUpdate.map(partId =>
          this.partService.ensurePartHasFreshZ2Data(partId)
        )
      );
    }

    // Step 2: Batch process supply lookup for items that need it
    const itemsNeedingSupply = itemsWithParts
      .filter(item => item.part !== null && item.index < maxItemsForSupplyProcessing)
      .map(item => ({
        partId: item.part!.id,
        mpn: item.part!.mpn,
        quantity: parseInt(item.bomItem.quantity) || 1,
        originalIndex: item.index,
      }));

    const supplyMap = new Map<number, bigint | null>();
    if (itemsNeedingSupply.length > 0) {
      this.logger.debug(`Processing supply lookup for ${itemsNeedingSupply.length} items`);
      const supplyResults = await this.batchFindBestSupplyIds(itemsNeedingSupply);
      supplyResults.forEach((supplyId, partId) => {
        supplyMap.set(partId, supplyId);
      });
    }

    // Step 3: Prepare all BOM item models for batch creation
    const bomItemModels: CreateBomItemModel[] = itemsWithParts.map((item, index) => ({
      bom_id: bomId,
      idx: index + 1,
      manufacturer_name: item.bomItem.manufacturer_name,
      match_reason: item.matchReason,
      matched_part_id: item.part?.id || null,
      matched_status: item.matchedStatus,
      mpn: item.bomItem.mpn,
      quantity: parseInt(item.bomItem.quantity) || 1,
      supply_id: item.part ? supplyMap.get(item.part.id) || null : null,
    }));

    // Step 4: Batch create all BOM items
    this.logger.debug(`Creating ${bomItemModels.length} BOM items in database`);
    const createdItems = await this.bomRepo.batchCreateBomItems(bomItemModels);

    this.logger.debug(`Successfully created ${createdItems.length} BOM items`);
    return createdItems as unknown as BomItem[];
  }

  private async batchFindBestSupplyIds(
    partQuantityPairs: Array<{ partId: number; mpn: string; quantity: number; originalIndex: number }>
  ): Promise<Map<number, bigint | null>> {
    const results = new Map<number, bigint | null>();

    // Process supply lookup for each part
    // Note: This could be further optimized with a batch supply lookup method
    await Promise.all(
      partQuantityPairs.map(async ({ partId, mpn, quantity }) => {
        try {
          const supplyId = await this.supplyService.findBestSupplyIdBasedOnQuantity(
            partId,
            mpn,
            quantity
          );
          results.set(partId, supplyId);
        } catch (error) {
          this.logger.warn(`Failed to find supply for part ${partId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
          results.set(partId, null);
        }
      })
    );

    return results;
  }

  private async createAndSaveBomItem(
    createBomItemModel: CreateBomItemModel,
  ): Promise<BomItem> {
    const fixedModel: CreateBomItemModel = createBomItemModel.idx
      ? { ...createBomItemModel }
      : {
          ...createBomItemModel,
          idx:
            (await this.bomRepo.findMaxIdxForBom(createBomItemModel.bom_id)) +
            1,
        };

    return this.bomRepo.createBomItem(
      fixedModel,
    ) as unknown as Promise<BomItem>;
  }

  async findAllBomItems(bomId: number): Promise<BomItem[]> {
    return this.bomRepo.findBomItems(bomId) as unknown as Promise<BomItem[]>;
  }

  async findBomItemById(id: number): Promise<BomItem | null> {
    return this.bomRepo.findBomItemById(
      id,
    ) as unknown as Promise<BomItem | null>;
  }

  async updateBomItem(
    updateBomItemInput: UpdateBomItemInput,
  ): Promise<BomItem> {
    const { id, ...data } = updateBomItemInput;
    return this.bomRepo.updateBomItem(id, {
      quantity: data.quantity,
      supply: data.supply_id ? { connect: { id: data.supply_id } } : undefined,
    }) as unknown as Promise<BomItem>;
  }

  async deleteBomItem(id: number): Promise<BomItem> {
    return this.bomRepo.deleteBomItem(id) as unknown as Promise<BomItem>;
  }

  async changeMatchedPart(
    bomItemId: bigint,
    newPartId: number,
  ): Promise<BomItem> {
    const bomItem = await this.bomRepo.findBomItemById(Number(bomItemId));
    if (!bomItem) {
      throw new Error(`BOM item with ID ${bomItemId} not found`);
    }

    const part = await this.partService.findOne(newPartId);
    if (!part) {
      throw new Error(`Part with ID ${newPartId} not found`);
    }

    // Ensure part has fresh Z2Data (lifecycle and RoHS status)
    await this.partService.ensurePartHasFreshZ2Data(newPartId);

    const bestSupplyId =
      await this.supplyService.findBestSupplyIdBasedOnQuantity(
        newPartId,
        part.mpn,
        bomItem.quantity || 1,
      );

    const updatedBomItem = await this.bomRepo.updateBomItem(Number(bomItemId), {
      match_reason: "User manually selected",
      matched_status: MatchedStatus.UserSelected,
      part: { connect: { id: newPartId } },
      supply: bestSupplyId ? { connect: { id: bestSupplyId } } : undefined,
    });

    return updatedBomItem as unknown as BomItem;
  }

  async findAllProjects(search?: string, limit: number = 5): Promise<string[]> {
    const results = await this.bomRepo.findDistinctProjects(search, limit);
    return results.map((result) => result.project as string);
  }

  private sanitizeBomInput<T extends { project?: string }>(input: T): T {
    return {
      ...input,
      project:
        input.project !== undefined ? input.project.trim() || null : undefined,
    };
  }
}
