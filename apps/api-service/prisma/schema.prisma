generator client {
  provider      = "prisma-client-js"
  output        = "../src/generated/client"
  binaryTargets = ["native", "debian-openssl-3.0.x", "debian-openssl-1.1.x", "darwin-arm64"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model manufacturer {
  id   Int    @id @default(autoincrement())
  name String @unique(map: "manufacturer_pk")
  part part[]
}


model supply {
  id                 BigInt     @id(map: "supply_pk") @default(autoincrement())
  part_id            Int
  seller_id          Int
  sku                String
  packaging          String?
  min_order_quantity Int?
  stock              Int?
  product_url        String?
  datasheet_url      String?
  lead_time          String?
  prices             Json?
  bom_items          bom_item[]
  part               part       @relation("PartToSupplies", fields: [part_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "supply_part_id_fk")
  seller             seller     @relation(fields: [seller_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "supply_seller_id_fk")

  @@unique([part_id, seller_id, sku], map: "supply_part_id_seller_id_sku_uindex")
  @@index([part_id], map: "supply_part_id_index")
}

model part {
  id                   Int                  @id @default(autoincrement())
  z2_part_id           Int?                 @unique
  manufacture_id       Int
  mpn                  String
  category             String?
  subcategory0         String?
  subcategory1         String?
  pl_name              String?
  description          String?
  family_series        String?
  lifecycle_status     String?
  rohs_status          String?
  last_z2data_update   DateTime?            @db.Timestamp(6)
  market_status        String?
  number_of_seller     Int?
  total_quantity_available Float?
  lowest_price         Float?
  min_lead_time_weeks  Int?
  max_lead_time_weeks  Int?
  bom_items            bom_item[]           @relation("PartToBomItem")
  manufacturer         manufacturer @relation(fields: [manufacture_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "part_manufacturer_id_fk")
  supplies             supply[]     @relation("PartToSupplies")
  alternative_cache    part_alternative_cache?
  alternative_items    part_alternative_items[] @relation("PartToAlternativeItems")

  @@index([mpn], map: "part_mpn_index")
}

model seller {
  id                  Int                   @id(map: "seller_pk") @default(autoincrement())
  z2_seller_id        Int?                  @unique(map: "seller_z2_seller_id_uindex")
  name                String                @unique(map: "seller_name_uindex")
  key                 String                @unique(map: "seller_key_uindex")
  supplier_preference supplier_preference[]
  supply              supply[]
}

model user_meta_data {
  id      Int    @id(map: "user_meta_data_pk") @default(autoincrement())
  user_id Int
  key     String
  value   Json

  @@unique([key, user_id])
}

model bom {
  id          Int        @id @default(autoincrement())
  name        String
  created_at  DateTime   @default(now()) @db.Timestamp(6)
  modified_at DateTime   @default(now()) @updatedAt @db.Timestamp(6)
  project     String?
  bom_items   bom_item[]
}

model bom_item {
  id                BigInt   @id @default(autoincrement())
  bom_id            Int
  matched_part_id   Int?
  quantity          Int?
  created_at        DateTime @default(now()) @db.Timestamp(6)
  modified_at       DateTime @default(now()) @updatedAt @db.Timestamp(6)
  mpn               String
  manufacturer_name String?
  matched_status    String
  idx               Int
  supply_id         BigInt?
  match_reason      String
  bom               bom      @relation(fields: [bom_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  part              part?    @relation("PartToBomItem", fields: [matched_part_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  supply            supply?  @relation(fields: [supply_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([bom_id, idx], map: "bom_item_pk")
}

model supplier_preference {
  id         BigInt  @id @default(autoincrement())
  seller_key String  @unique
  seller_id  Int?
  enabled    Boolean @default(true)
  config     Json?
  seller     seller? @relation(fields: [seller_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([seller_id], map: "supplier_preference_seller_id_index")
}

model currency_rates {
  id           Int      @id @default(autoincrement())
  base         String   @default("USD")
  rates        Json
  last_updated DateTime @default(now())
}

model packaging_alias {
  id              Int      @id @default(autoincrement())
  original_name   String   @unique
  normalized_name String
  created_at      DateTime @default(now()) @db.Timestamp(6)
  updated_at      DateTime @default(now()) @updatedAt @db.Timestamp(6)
}

model manufacturer_alias {
  id              Int      @id @default(autoincrement())
  original_name   String   @unique
  normalized_name String
  created_at      DateTime @default(now()) @db.Timestamp(6)
  updated_at      DateTime @default(now()) @updatedAt @db.Timestamp(6)
}

model part_alternative_cache {
  id                   Int                        @id @default(autoincrement())
  part_id              Int                        @unique
  z2_part_id           Int
  mpn                  String
  company_name         String
  part_lifecycle       String
  rohs_flag            String?
  data_sheet           String
  total_crosses_found  Int
  num_found            Int
  page_number          Int
  created_at           DateTime                   @default(now()) @db.Timestamp(6)
  updated_at           DateTime                   @default(now()) @updatedAt @db.Timestamp(6)
  part                 part                       @relation(fields: [part_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  alternative_items    part_alternative_items[]

  @@index([part_id], map: "part_alternative_cache_part_id_index")
  @@index([updated_at], map: "part_alternative_cache_updated_at_index")
}

model part_alternative_items {
  id               Int                      @id @default(autoincrement())
  cache_id         Int
  part_id          Int
  mpn              String
  company_name     String
  cross_type       String
  cross_comment    String
  part_description String
  part_lifecycle   String
  rohs_flag        String?
  package          String
  data_sheet       String
  cache            part_alternative_cache  @relation(fields: [cache_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  part             part                    @relation("PartToAlternativeItems", fields: [part_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([cache_id], map: "part_alternative_items_cache_id_index")
  @@index([part_id], map: "part_alternative_items_part_id_index")
}
