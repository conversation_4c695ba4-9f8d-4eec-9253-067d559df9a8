import { DataAwsIamPolicyDocument } from "@cdktf/provider-aws/lib/data-aws-iam-policy-document";
import { IamRolePolicy } from "@cdktf/provider-aws/lib/iam-role-policy";
import { LbTargetGroup } from "@cdktf/provider-aws/lib/lb-target-group";
import { AwsProvider } from "@cdktf/provider-aws/lib/provider";
import { SecurityGroup } from "@cdktf/provider-aws/lib/security-group";
import { TerraformStack } from "cdktf";
import { Construct } from "constructs";

import {
  Cluster,
  EcsRoles,
  MigrationTaskDefinition,
  RemoteBackendConstruct,
  Repository,
  Service,
  TaskDefinition,
} from "../constructs";

type EcsStackProps = {
  awsRegion: string;
  environment: string;
  projectName: string;
  servicesToDeploy: string[];
  ecrRepositories: Record<string, Repository>;
  vpcId: string;
  privateSubnetIds: string[];
  targetGroups: Record<string, LbTargetGroup>;
  imageTag: string;
  serviceConfigs: Record<
    string,
    {
      accessToDatabase: boolean;
      containerPort: number;
      desiredCount: number;
      cpu: string;
      memory: string;
      containerEnvironment?: Record<string, string | undefined>;
    }
  >;
  serviceSecurityGroups: Record<string, SecurityGroup>;
  dbSecretArn: string;
  dbKmsKeyArn: string;
  remoteBackendBucketName: string;
  remoteBackendStateKeyPrefix: string;
  remoteBackendLockTableName: string;
  tags: Record<string, string>;
};

export class EcsStack extends TerraformStack {
  public readonly cluster: Cluster;
  public readonly services: Record<string, Service> = {};
  public readonly securityGroups: Record<string, SecurityGroup> = {};
  public readonly migrationTasks: Record<string, MigrationTaskDefinition> = {};

  constructor(scope: Construct, id: string, props: EcsStackProps) {
    super(scope, id);

    new AwsProvider(this, "AWS", {
      region: props.awsRegion,
    });

    new RemoteBackendConstruct(this, "s3-backend", {
      awsRegion: props.awsRegion,
      bucketName: props.remoteBackendBucketName,
      lockTableName: props.remoteBackendLockTableName,
      stackId: id,
      stateKeyPrefix: props.remoteBackendStateKeyPrefix,
    });

    const clusterName = `${props.projectName}-${props.environment}-cluster`;
    this.cluster = new Cluster(this, "cluster", {
      name: clusterName,
      tags: props.tags,
    });

    const iamRoles = new EcsRoles(this, "iam-roles", clusterName);

    const dbSecretAccessPolicyDoc = new DataAwsIamPolicyDocument(
      this,
      "db-secret-access-policy-doc",
      {
        statement: [
          {
            actions: ["secretsmanager:GetSecretValue"],
            effect: "Allow",
            resources: [props.dbSecretArn],
          },
          {
            actions: ["kms:Decrypt"],
            effect: "Allow",
            resources: [props.dbKmsKeyArn],
          },
        ],
      },
    );

    new IamRolePolicy(this, "db-secret-access-policy", {
      name: `${clusterName}-db-secret-access-policy`,
      policy: dbSecretAccessPolicyDoc.json,
      role: iamRoles.executionRole.name,
    });

    this.securityGroups = props.serviceSecurityGroups;

    props.servicesToDeploy.forEach((serviceName) => {
      const serviceConfig = props.serviceConfigs[serviceName];
      if (!serviceConfig) {
        console.warn(
          `No configuration found for service ${serviceName}, skipping`,
        );
        return;
      }

      const namePrefix = `${props.projectName}-${props.environment}-${serviceName}`;

      const serviceSecurityGroup = props.serviceSecurityGroups[serviceName];
      if (!serviceSecurityGroup) {
        throw new Error(
          `Security group for service ${serviceName} not provided to EcsStack.`,
        );
      }

      const repository = props.ecrRepositories[serviceName];
      if (!repository) {
        throw new Error(
          `Repository for service ${serviceName} not provided to EcsStack.`,
        );
      }

      const databaseSecrets = {
        DATABASE_HOST: {
          secretArn: props.dbSecretArn,
          secretKey: "DATABASE_HOST",
        },
        DATABASE_NAME: {
          secretArn: props.dbSecretArn,
          secretKey: "DATABASE_NAME",
        },
        DATABASE_PASSWORD: {
          secretArn: props.dbSecretArn,
          secretKey: "DATABASE_PASSWORD",
        },
        DATABASE_PORT: {
          secretArn: props.dbSecretArn,
          secretKey: "DATABASE_PORT",
        },
        DATABASE_URL: {
          secretArn: props.dbSecretArn,
          secretKey: "DATABASE_URL",
        },
        DATABASE_USERNAME: {
          secretArn: props.dbSecretArn,
          secretKey: "DATABASE_USERNAME",
        },
      };

      const taskDefinition = new TaskDefinition(
        this,
        `${serviceName}-task-def`,
        {
          awsRegion: props.awsRegion,
          containerPort: serviceConfig.containerPort,
          cpu: serviceConfig.cpu,
          environment: serviceConfig.containerEnvironment,
          executionRoleArn: iamRoles.executionRole.arn,
          family: namePrefix,
          imageTag: props.imageTag,
          memory: serviceConfig.memory,
          repository,
          secrets: serviceConfig.accessToDatabase ? databaseSecrets : undefined,
          tags: props.tags,
          taskRoleArn: iamRoles.taskRole.arn,
        },
      );

      const targetGroup = props.targetGroups[serviceName];
      if (!targetGroup) {
        throw new Error(
          `Target group for service ${serviceName} not provided to EcsStack.`,
        );
      }

      this.services[serviceName] = new Service(this, `${serviceName}-service`, {
        cluster: this.cluster,
        containerName: namePrefix,
        containerPort: serviceConfig.containerPort,
        desiredCount: serviceConfig.desiredCount,
        name: `${namePrefix}-service`,
        securityGroupIds: [serviceSecurityGroup.id],
        subnets: props.privateSubnetIds,
        targetGroupArn: targetGroup.arn,
        taskDefinition,
        vpcId: props.vpcId,
      });

      if (serviceConfig.accessToDatabase) {
        this.migrationTasks[serviceName] = new MigrationTaskDefinition(
          this,
          `${serviceName}-migration-task`,
          {
            awsRegion: props.awsRegion,
            cpu: "256",
            executionRoleArn: iamRoles.executionRole.arn,
            family: namePrefix,
            // It should not be props.imageTag as in the pipelines, we are deploying this stack after the container for database migration is executed.
            imageTag: "latest",
            memory: "512",
            repository,
            secrets: databaseSecrets,
            serviceName,
            tags: props.tags,
            taskRoleArn: iamRoles.taskRole.arn,
          },
        );
      }
    });
  }
}
